// Key term extraction utilities [SF][RP]

export interface KeyTerm {
  term: string;
  frequency: number;
  context: string[];
}

export interface ExtractionResult {
  keyTerms: KeyTerm[];
  totalProcessed: number;
  technicalContentCount: number;
}

export class KeyTermExtractor {
  private readonly commonWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
    'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does',
    'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that',
    'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her',
    'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
  ]);

  /**
   * Extract key terms from technical content [CA][CMV]
   */
  extractKeyTerms(
    contents: string[], 
    minFrequency: number = 2,
    maxTerms: number = 20
  ): ExtractionResult {
    const termFrequency = new Map<string, { count: number; contexts: Set<string> }>();
    let technicalContentCount = 0;

    // Process each content item [PA]
    for (const content of contents) {
      if (!content || content.trim().length === 0) continue;
      
      technicalContentCount++;
      const terms = this.extractTermsFromText(content);
      
      for (const term of terms) {
        if (!termFrequency.has(term)) {
          termFrequency.set(term, { count: 0, contexts: new Set() });
        }
        
        const entry = termFrequency.get(term)!;
        entry.count++;
        entry.contexts.add(this.getTermContext(content, term));
      }
    }

    // Convert to sorted key terms [SF]
    const keyTerms: KeyTerm[] = Array.from(termFrequency.entries())
      .filter(([_, data]) => data.count >= minFrequency)
      .map(([term, data]) => ({
        term,
        frequency: data.count,
        context: Array.from(data.contexts).slice(0, 3) // Max 3 context examples
      }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, maxTerms);

    return {
      keyTerms,
      totalProcessed: contents.length,
      technicalContentCount
    };
  }

  /**
   * Extract meaningful terms from text [REH]
   */
  private extractTermsFromText(text: string): string[] {
    // Clean and normalize text
    const cleaned = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, ' ') // Keep hyphens for compound terms
      .replace(/\s+/g, ' ')
      .trim();

    // Extract terms (1-3 words) [CMV]
    const words = cleaned.split(' ').filter(word => 
      word.length > 2 && 
      !this.commonWords.has(word) &&
      !this.isNumeric(word)
    );

    const terms: string[] = [];
    
    // Single words
    terms.push(...words);
    
    // Bigrams (2-word combinations)
    for (let i = 0; i < words.length - 1; i++) {
      const bigram = `${words[i]} ${words[i + 1]}`;
      if (this.isMeaningfulTerm(bigram)) {
        terms.push(bigram);
      }
    }
    
    // Trigrams (3-word combinations) - only for very technical terms
    for (let i = 0; i < words.length - 2; i++) {
      const trigram = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
      if (this.isTechnicalTrigram(trigram)) {
        terms.push(trigram);
      }
    }

    return terms;
  }

  /**
   * Get context around a term [SF]
   */
  private getTermContext(text: string, term: string): string {
    const index = text.toLowerCase().indexOf(term.toLowerCase());
    if (index === -1) return '';

    const start = Math.max(0, index - 30);
    const end = Math.min(text.length, index + term.length + 30);
    
    return text.substring(start, end).trim();
  }

  /**
   * Check if a term is meaningful (not just common words) [CMV]
   */
  private isMeaningfulTerm(term: string): boolean {
    const words = term.split(' ');
    return words.some(word => !this.commonWords.has(word)) && 
           words.length <= 3;
  }

  /**
   * Check if a trigram is technical [SF]
   */
  private isTechnicalTrigram(trigram: string): boolean {
    const technicalPatterns = [
      /machine learning/i,
      /artificial intelligence/i,
      /deep learning/i,
      /neural network/i,
      /data science/i,
      /software development/i,
      /web development/i
    ];

    return technicalPatterns.some(pattern => pattern.test(trigram));
  }

  /**
   * Check if string is numeric [IV]
   */
  private isNumeric(str: string): boolean {
    return /^\d+$/.test(str);
  }

  /**
   * Generate summary statistics [SD]
   */
  generateSummary(result: ExtractionResult): string {
    const { keyTerms, totalProcessed, technicalContentCount } = result;
    
    const topTerms = keyTerms.slice(0, 5).map(term => 
      `${term.term} (${term.frequency}x)`
    ).join(', ');

    return `
Processed ${totalProcessed} items, ${technicalContentCount} technical.
Top terms: ${topTerms}
Total unique terms: ${keyTerms.length}
    `.trim();
  }
}
