// Unit tests for SmartDispatcher [TDT][REH]

import { SmartDispatcher } from '../services/smart-dispatcher';
import { FilterService } from '../services/filter.service';
import { OllamaService } from '../services/ollama.service';
import { defaultKeywordConfig } from '../config/keywords';
import { defaultOllamaConfig } from '../config/ollama';
import { VideoMetadata } from '../types/analysis';

// Suppress console outputs during testing [TDT]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
});

// Mock Ollama service for testing [TDT]
class MockOllamaService extends OllamaService {
  constructor() {
    super(defaultOllamaConfig);
  }

  async summarize(content: string, metadata: VideoMetadata, keywordResult: any) {
    return {
      isTechnical: true,
      method: 'keyword-confirmed' as const,
      confidence: 0.95,
      matchedKeywords: keywordResult.matchedKeywords,
      aiSummary: ['AI', 'Testing', 'Mock'],
      category: 'AI' as const,
      enhancedBy: 'ollama' as const,
      videoId: metadata.id,
      title: metadata.title,
      publishedAt: metadata.publishedAt,
      analysisTimestamp: new Date().toISOString()
    };
  }

  async analyzeAndSummarize(content: string, metadata: VideoMetadata) {
    const isTechnical = content.toLowerCase().includes('programming');
    return {
      isTechnical,
      method: 'ollama-analysis' as const,
      confidence: 0.8,
      aiSummary: isTechnical ? ['Programming', 'Analysis'] : [],
      category: 'Development' as const,
      reasoning: 'Mock analysis',
      enhancedBy: 'ollama' as const,
      videoId: metadata.id,
      title: metadata.title,
      publishedAt: metadata.publishedAt,
      analysisTimestamp: new Date().toISOString()
    };
  }
}

describe('SmartDispatcher', () => {
  let dispatcher: SmartDispatcher;
  let filterService: FilterService;
  let mockOllamaService: MockOllamaService;

  const mockMetadata: VideoMetadata = {
    id: 'test123',
    title: 'Test Video',
    description: 'Test description',
    channelId: 'testchannel',
    channelTitle: 'Test Channel',
    publishedAt: '2025-01-01T00:00:00Z',
    duration: '10:00'
  };

  beforeEach(() => {
    filterService = new FilterService(defaultKeywordConfig);
    mockOllamaService = new MockOllamaService();
    dispatcher = new SmartDispatcher(filterService, mockOllamaService);
  });

  describe('Hard Exclusions', () => {
    it('should exclude clear entertainment content', async () => {
      const content = 'Funny cat compilation #comedy #viral #shorts';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
      expect(result.confidence).toBeGreaterThan(0.95);
      expect(result.excludedBy).toContain('Entertainment');
    });

    it('should exclude movie/film content', async () => {
      const content = 'Marvel movie trailer with amazing special effects';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
    });

    it('should exclude music content', async () => {
      const content = 'Amazing song performance at concert hall';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
    });
  });

  describe('Technical Content Recognition', () => {
    it('should recognize clear AI/ML content', async () => {
      const content = 'Machine learning tutorial with Python and TensorFlow deep learning';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.aiSummary).toBeDefined();
      expect(result.enhancedBy).toBe('ollama');
    });

    it('should recognize development content', async () => {
      const content = 'JavaScript programming tutorial with React and Node.js';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.matchedKeywords?.length).toBeGreaterThan(0);
    });

    it('should recognize agile/testing content', async () => {
      const content = 'Scrum methodology and test-driven development practices';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
    });
  });

  describe('Borderline Cases', () => {
    it('should use LLM for ambiguous content', async () => {
      const content = 'Short discussion about programming concepts';
      const result = await dispatcher.dispatch(content, mockMetadata);

      // Should be technical due to 'programming' keyword
      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
      expect(result.enhancedBy).toBe('ollama');
    });

    it('should handle mixed signals correctly', async () => {
      const content = 'Programming tutorial but also funny comedy elements';
      const result = await dispatcher.dispatch(content, mockMetadata);

      // Should be excluded due to comedy keyword
      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('keyword-exclusion');
    });
  });

  describe('Batch Processing', () => {
    it('should process multiple items correctly', async () => {
      const items = [
        { content: 'Machine learning with Python', metadata: { ...mockMetadata, id: 'tech1' } },
        { content: 'Funny cat video compilation', metadata: { ...mockMetadata, id: 'fun1' } },
        { content: 'JavaScript programming guide', metadata: { ...mockMetadata, id: 'tech2' } }
      ];

      const results = await dispatcher.dispatchBatch(items);

      expect(results).toHaveLength(3);
      expect(results[0].isTechnical).toBe(true);  // ML content
      expect(results[1].isTechnical).toBe(false); // Cat video
      expect(results[2].isTechnical).toBe(true);  // JS content
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty content', async () => {
      const result = await dispatcher.dispatch('', mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('keyword-exclusion');
    });

    it('should handle very short content', async () => {
      const result = await dispatcher.dispatch('AI', mockMetadata);

      expect(result.isTechnical).toBe(true);
      expect(result.method).toBe('keyword-confirmed');
    });

    it('should detect spam content', async () => {
      const spamContent = 'aaaaaaaaaaaaaaaaaaaaaa #spam #viral #trending';
      const result = await dispatcher.dispatch(spamContent, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
    });
  });

  describe('Spam Detection', () => {
    it('should detect repetitive character spam', async () => {
      const content = 'AI tutorial aaaaaaaaaaaaaaaaaaaaaa';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
      expect(result.excludedBy).toContain('Spam');
    });

    it('should detect excessive emoji content', async () => {
      const content = 'AI tutorial 😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀😀';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
      expect(result.excludedBy).toContain('Spam');
    });

    it('should detect excessive hashtag spam', async () => {
      const content = 'AI tutorial #tag1 #tag2 #tag3 #tag4 #tag5 #tag6 #tag7 #tag8 #tag9 #tag10 #tag11 #tag12';
      const result = await dispatcher.dispatch(content, mockMetadata);

      expect(result.isTechnical).toBe(false);
      expect(result.method).toBe('hard-exclusion');
      expect(result.excludedBy).toContain('Spam');
    });
  });

  describe('LLM Analysis Requirements', () => {
    it('should require LLM analysis for borderline content', async () => {
      // Mock a case where keyword filter doesn't find technical content but it's borderline
      const content = 'This is a short discussion about concepts that might be technical';
      const result = await dispatcher.dispatch(content, mockMetadata);

      // This should trigger keyword exclusion since no technical keywords are found
      expect(result.method).toBe('keyword-exclusion');
      expect(result.isTechnical).toBe(false);
    });
  });

  describe('Batch Processing', () => {
    it('should handle batch processing errors gracefully', async () => {
      const items = [
        { content: 'AI tutorial', metadata: mockMetadata },
        { content: 'Machine learning', metadata: mockMetadata }
      ];

      // Mock one successful and one failed dispatch
      const originalDispatch = dispatcher.dispatch;
      jest.spyOn(dispatcher, 'dispatch')
        .mockResolvedValueOnce({
          isTechnical: true,
          method: 'keyword-confirmed',
          confidence: 0.9,
          videoId: 'test1',
          title: 'Test 1',
          publishedAt: '2025-01-01T00:00:00Z',
          analysisTimestamp: '2025-01-01T00:00:00Z'
        })
        .mockRejectedValueOnce(new Error('Processing failed'));

      const results = await dispatcher.dispatchBatch(items);

      expect(results).toHaveLength(2);
      expect(results[0].isTechnical).toBe(true);
      expect(results[1].isTechnical).toBe(false);
      expect(results[1].error).toBe('batch-processing-failed');
      expect(results[1].title).toBe('Processing failed');
    });

    it('should add delays between batches', async () => {
      // Create enough items to trigger multiple batches (assuming BATCH_SIZE is 5)
      const items = Array.from({ length: 12 }, (_, i) => ({
        content: `AI tutorial ${i}`,
        metadata: { ...mockMetadata, id: `test${i}` }
      }));

      const sleepSpy = jest.spyOn(dispatcher as any, 'sleep').mockResolvedValue(undefined);

      await dispatcher.dispatchBatch(items);

      // Should have called sleep between batches
      expect(sleepSpy).toHaveBeenCalled();
    });
  });

  describe('Sleep Utility', () => {
    it('should sleep for specified duration', async () => {
      const start = Date.now();
      await (dispatcher as any).sleep(100);
      const end = Date.now();

      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some tolerance
    });
  });
});
