// Content filtering service ported from PowerShell logic [SF][RP][DRY]

import { KeywordConfig } from '../config/keywords';

export interface FilterResult {
  isTechnical: boolean;
  matchedKeywords: string[];
  excludedBy?: string;
}

export class FilterService {
  constructor(private config: KeywordConfig) {}

  /**
   * Filters content based on technical keywords and exclusion rules [REH]
   * Ported from ExtractTechnicalLines.ps1 logic
   */
  filterContent(content: string): FilterResult {
    const contentLower = content.toLowerCase();
    const matchedKeywords: string[] = [];
    let isTechnical = false;

    // Check for inclusion keywords [CMV]
    for (const keyword of this.config.includeKeywords) {
      const regex = new RegExp(`\\b${this.escapeRegex(keyword.toLowerCase())}\\b`, 'i');
      if (regex.test(contentLower)) {
        isTechnical = true;
        matchedKeywords.push(keyword);
      }
    }

    // If technical, check exclusion criteria [CA]
    if (isTechnical) {
      // Check exclusion keywords
      for (const exclude of this.config.excludeKeywords) {
        const regex = new RegExp(`\\b${this.escapeRegex(exclude.toLowerCase())}\\b`, 'i');
        if (regex.test(contentLower)) {
          return {
            isTechnical: false,
            matchedKeywords,
            excludedBy: `keyword: ${exclude}`
          };
        }
      }

      // Check exclusion hashtags
      const hashtagMatches = contentLower.match(/#(\w+)/g);
      if (hashtagMatches) {
        for (const hashtagMatch of hashtagMatches) {
          const hashtag = hashtagMatch.substring(1); // Remove #
          if (this.config.excludeHashtags.includes(hashtag)) {
            return {
              isTechnical: false,
              matchedKeywords,
              excludedBy: `hashtag: #${hashtag}`
            };
          }
        }
      }
    }

    return {
      isTechnical,
      matchedKeywords
    };
  }

  /**
   * Escapes special regex characters [SF]
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Batch filter multiple content items [PA]
   */
  filterMultiple(contents: string[]): FilterResult[] {
    return contents.map(content => this.filterContent(content));
  }
}
