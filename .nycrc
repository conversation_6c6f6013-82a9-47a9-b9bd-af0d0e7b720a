{"extends": "@istanbuljs/nyc-config-typescript", "all": true, "check-coverage": true, "reporter": ["html", "text-summary", "lcov"], "include": ["src/**/*.ts"], "exclude": ["src/**/*.d.ts", "src/test/**/*.ts", "src/index.ts"], "branches": 80, "lines": 80, "functions": 80, "statements": 80, "watermarks": {"lines": [70, 90], "functions": [70, 90], "branches": [70, 90], "statements": [70, 90]}, "report-dir": "./coverage"}