// Unit tests for FilterService [TDT][REH]

import { FilterService, FilterResult } from '../services/filter.service';
import { defaultKeywordConfig } from '../config/keywords';

// Selective console suppression for testing [TDT][REH]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

// Expected messages that should be suppressed during successful tests
const EXPECTED_MESSAGES = [
  'Processing batch',
  '🔍 Analyzing channel:',
  '📹 Found',
  '🤖 Starting smart analysis',
  'Ollama attempt',
  'Failed to analyze channel'
];

const shouldSuppressMessage = (message: string): boolean => {
  return EXPECTED_MESSAGES.some(expected =>
    message.includes(expected)
  );
};

beforeAll(() => {
  console.log = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    if (!shouldSuppressMessage(message)) {
      originalLog(...args); // Show unexpected messages
    }
  });

  console.warn = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    if (!shouldSuppressMessage(message)) {
      originalWarn(...args); // Show unexpected warnings
    }
  });

  console.error = jest.fn().mockImplementation((...args) => {
    const message = args.join(' ');
    if (!shouldSuppressMessage(message)) {
      originalError(...args); // Show unexpected errors
    }
  });
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
});

describe('FilterService', () => {
  let filterService: FilterService;

  beforeEach(() => {
    filterService = new FilterService(defaultKeywordConfig);
  });

  describe('filterContent', () => {
    it('should identify technical content with AI keywords', () => {
      const content = 'This video discusses machine learning algorithms and AI development';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(true);
      expect(result.matchedKeywords).toContain('machine learning');
      expect(result.matchedKeywords).toContain('AI');
    });

    it('should exclude content with movie keywords', () => {
      const content = 'AI in The Matrix movie with great special effects';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(false);
      expect(result.excludedBy).toBeDefined();
      expect(result.excludedBy).toContain('movie');
    });

    it('should exclude content with non-technical hashtags', () => {
      const content = 'Great AI tutorial #funny #comedy #shorts';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(false);
      expect(result.excludedBy).toBeDefined();
      expect(result.excludedBy).toContain('comedy');
    });

    it('should exclude content when first hashtag in list is excluded', () => {
      const content = 'AI tutorial #funny #tech #programming';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(false);
      expect(result.excludedBy).toBeDefined();
      expect(result.excludedBy).toContain('keyword: funny');
    });

    it('should exclude content when later hashtag in list is excluded', () => {
      const content = 'AI tutorial #tech #programming #comedy';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(false);
      expect(result.excludedBy).toBeDefined();
      expect(result.excludedBy).toContain('keyword: comedy');
    });

    it('should handle empty content', () => {
      const result = filterService.filterContent('');

      expect(result.isTechnical).toBe(false);
      expect(result.matchedKeywords).toHaveLength(0);
    });

    it('should be case insensitive', () => {
      const content = 'MACHINE LEARNING and deep LEARNING tutorial';
      const result = filterService.filterContent(content);

      expect(result.isTechnical).toBe(true);
      expect(result.matchedKeywords.length).toBeGreaterThan(0);
    });
  });

  describe('filterMultiple', () => {
    it('should filter multiple content items', () => {
      const contents = [
        'AI and machine learning tutorial',
        'Funny cat video #comedy',
        'Deep learning with Python programming'
      ];

      const results = filterService.filterMultiple(contents);

      expect(results).toHaveLength(3);
      expect(results[0].isTechnical).toBe(true);
      expect(results[1].isTechnical).toBe(false);
      expect(results[2].isTechnical).toBe(true);
    });
  });
});
