// YouTube watch history analysis service
import * as fs from 'fs';
import * as path from 'path';
import { VideoData } from './youtube.service';

export interface WatchHistoryEntry {
  header: string;
  title: string;
  titleUrl: string;
  time: string;
  products: string[];
  activityControls: string[];
}

export class WatchHistoryService {
  /**
   * Load and parse YouTube watch history JSON file
   */
  static loadWatchHistory(filePath: string): WatchHistoryEntry[] {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Failed to load watch history: ${error}`);
    }
  }

  /**
   * Extract video IDs from watch history for a specific month
   */
  static extractVideoIds(
    history: WatchHistoryEntry[], 
    targetMonth: string
  ): string[] {
    const [year, month] = targetMonth.split('-');
    const startDate = new Date(parseInt(year), parseInt(month) - 1, 1);
    const endDate = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59);

    return history
      .filter(entry => {
        const watchTime = new Date(entry.time);
        return watchTime >= startDate && watchTime <= endDate;
      })
      .map(entry => this.extractVideoId(entry.titleUrl))
      .filter(Boolean) as string[];
  }

  /**
   * Extract video ID from YouTube URL
   */
  private static extractVideoId(url: string): string | null {
    const match = url.match(/[?&]v=([^&]+)/);
    return match ? match[1] : null;
  }

  /**
   * Convert to VideoData format for existing analysis pipeline
   */
  static async convertToVideoData(
    videoIds: string[],
    youtubeService: any
  ): Promise<VideoData[]> {
    const videos: VideoData[] = [];
    
    for (const videoId of videoIds) {
      try {
        const videoData = await youtubeService.getVideoDetails(videoId);
        videos.push(videoData);
      } catch (error) {
        console.warn(`Could not fetch details for video ${videoId}:`, error);
      }
    }
    
    return videos;
  }
}
