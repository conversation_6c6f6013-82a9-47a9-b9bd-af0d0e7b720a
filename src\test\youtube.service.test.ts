// Unit tests for YouTubeAnalyzer [TDT][REH]

import { YouTubeAnalyzer } from '../services/youtube.service';
import { SmartDispatcher } from '../services/smart-dispatcher';
import { FilterService } from '../services/filter.service';
import { OllamaService } from '../services/ollama.service';
import { defaultKeywordConfig } from '../config/keywords';
import { defaultOllamaConfig } from '../config/ollama';

// Suppress console outputs during testing [TDT]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
});

// Mock dependencies [TDT]
jest.mock('googleapis');
jest.mock('youtube-transcript');

describe('YouTubeAnalyzer', () => {
  let youtubeAnalyzer: YouTubeAnalyzer;
  let mockDispatcher: jest.Mocked<SmartDispatcher>;

  beforeEach(() => {
    // Create mock dispatcher
    const filterService = new FilterService(defaultKeywordConfig);
    const ollamaService = new OllamaService(defaultOllamaConfig);
    mockDispatcher = new SmartDispatcher(filterService, ollamaService) as jest.Mocked<SmartDispatcher>;
    
    // Mock dispatcher methods
    mockDispatcher.dispatch = jest.fn();
    mockDispatcher.dispatchBatch = jest.fn();

    youtubeAnalyzer = new YouTubeAnalyzer('test-api-key', mockDispatcher);
  });

  describe('constructor', () => {
    it('should initialize with API key and dispatcher', () => {
      expect(youtubeAnalyzer).toBeInstanceOf(YouTubeAnalyzer);
    });

    it('should work without dispatcher', () => {
      const analyzer = new YouTubeAnalyzer('test-api-key');
      expect(analyzer).toBeInstanceOf(YouTubeAnalyzer);
    });
  });

  describe('buildVideoContent', () => {
    it('should combine video metadata into content string', () => {
      const mockVideo = {
        id: 'test123',
        title: 'Test Video Title',
        description: 'Test video description with technical content about AI and machine learning',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'testchannel',
        duration: '10:00',
        transcript: 'This is a test transcript about programming and development'
      };

      // Access private method via type assertion for testing
      const content = (youtubeAnalyzer as any).buildVideoContent(mockVideo);

      expect(content).toContain('Test Video Title');
      expect(content).toContain('Test video description');
      expect(content).toContain('This is a test transcript');
    });

    it('should handle missing transcript', () => {
      const mockVideo = {
        id: 'test123',
        title: 'Test Video Title',
        description: 'Test description',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'testchannel',
        duration: '10:00'
      };

      const content = (youtubeAnalyzer as any).buildVideoContent(mockVideo);

      expect(content).toContain('Test Video Title');
      expect(content).toContain('Test description');
      expect(content).not.toContain('undefined');
    });

    it('should truncate long descriptions and transcripts', () => {
      const longText = 'A'.repeat(1000);
      const mockVideo = {
        id: 'test123',
        title: 'Test Video Title',
        description: longText,
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'testchannel',
        duration: '10:00',
        transcript: longText
      };

      const content = (youtubeAnalyzer as any).buildVideoContent(mockVideo);

      // Should be truncated: title + "Description: " + 500 chars + "\nTranscript: " + 1000 chars
      expect(content.length).toBeLessThan(1560);
      expect(content).toContain('Test Video Title');
      expect(content).toContain('Description: ' + 'A'.repeat(500));
      expect(content).toContain('Transcript: ' + 'A'.repeat(1000));
    });
  });

  describe('videoToMetadata', () => {
    it('should convert VideoData to VideoMetadata', () => {
      const mockVideo = {
        id: 'test123',
        title: 'Test Video Title',
        description: 'Test description',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'original-channel',
        duration: '10:00'
      };

      const metadata = (youtubeAnalyzer as any).videoToMetadata(mockVideo, 'new-channel-id');

      expect(metadata).toEqual({
        id: 'test123',
        title: 'Test Video Title',
        description: 'Test description',
        channelId: 'new-channel-id', // Should use provided channelId
        channelTitle: 'Test Channel',
        publishedAt: '2025-01-01T00:00:00Z',
        duration: '10:00'
      });
    });
  });

  describe('generateSummary', () => {
    it('should generate comprehensive summary from analysis results', () => {
      const mockResults = [
        {
          isTechnical: true,
          method: 'keyword-confirmed' as const,
          confidence: 0.95,
          aiSummary: ['AI', 'Python'],
          category: 'AI' as const,
          videoId: 'test1',
          title: 'Test 1',
          publishedAt: '2025-01-01T00:00:00Z',
          analysisTimestamp: '2025-01-01T00:00:00Z'
        },
        {
          isTechnical: true,
          method: 'ollama-analysis' as const,
          confidence: 0.85,
          aiSummary: ['JavaScript', 'React'],
          category: 'Development' as const,
          videoId: 'test2',
          title: 'Test 2',
          publishedAt: '2025-01-01T00:00:00Z',
          analysisTimestamp: '2025-01-01T00:00:00Z'
        },
        {
          isTechnical: false,
          method: 'hard-exclusion' as const,
          confidence: 0.99,
          videoId: 'test3',
          title: 'Test 3',
          publishedAt: '2025-01-01T00:00:00Z',
          analysisTimestamp: '2025-01-01T00:00:00Z'
        }
      ];

      const summary = (youtubeAnalyzer as any).generateSummary(mockResults);

      expect(summary.aiKeywords).toContain('AI');
      expect(summary.aiKeywords).toContain('Python');
      expect(summary.aiKeywords).toContain('JavaScript');
      expect(summary.aiKeywords).toContain('React');
      expect(summary.aiKeywords).toHaveLength(4); // No duplicates

      expect(summary.categories['AI']).toBe(1);
      expect(summary.categories['Development']).toBe(1);

      expect(summary.methods['keyword-confirmed']).toBe(1);
      expect(summary.methods['ollama-analysis']).toBe(1);
      expect(summary.methods['hard-exclusion']).toBe(1);
    });

    it('should handle empty results', () => {
      const summary = (youtubeAnalyzer as any).generateSummary([]);

      expect(summary.aiKeywords).toEqual([]);
      expect(summary.categories).toEqual({});
      expect(summary.methods).toEqual({});
    });

    it('should remove duplicate keywords', () => {
      const mockResults = [
        {
          isTechnical: true,
          method: 'keyword-confirmed' as const,
          confidence: 0.95,
          aiSummary: ['AI', 'Python', 'AI'], // Duplicate AI
          category: 'AI' as const,
          videoId: 'test1',
          title: 'Test 1',
          publishedAt: '2025-01-01T00:00:00Z',
          analysisTimestamp: '2025-01-01T00:00:00Z'
        }
      ];

      const summary = (youtubeAnalyzer as any).generateSummary(mockResults);

      expect(summary.aiKeywords).toEqual(['AI', 'Python']); // No duplicates
    });
  });

  describe('error handling', () => {
    it('should handle dispatcher errors gracefully', async () => {
      mockDispatcher.dispatchBatch.mockRejectedValueOnce(new Error('Dispatcher error'));

      const config = {
        targetMonth: '2025-01',
        maxVideos: 10,
        watchHistoryFile: 'test-history.json'
      };

      // Mock watch history service
      const mockHistory = [{
        header: 'YouTube',
        title: 'Test Video',
        titleUrl: 'https://www.youtube.com/watch?v=test123',
        time: '2025-01-15T10:00:00Z',
        products: ['YouTube'],
        activityControls: ['YouTube-Wiedergabeverlauf']
      }];

      jest.doMock('../services/watch-history.service', () => ({
        WatchHistoryService: {
          loadWatchHistory: jest.fn().mockReturnValue(mockHistory),
          extractVideoIds: jest.fn().mockReturnValue(['test123'])
        }
      }));

      // Mock getVideoDetails
      jest.spyOn(youtubeAnalyzer as any, 'getVideoDetails').mockResolvedValue({
        id: 'test123',
        title: 'Test Video',
        description: 'Test description',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'testchannel',
        duration: '10:00'
      });

      // Should throw because dispatcher fails
      await expect(youtubeAnalyzer.analyzeWatchHistory(config)).rejects.toThrow('Watch history analysis failed');
    });
  });

  describe('integration scenarios', () => {
    it('should handle empty watch history', async () => {
      const config = {
        targetMonth: '2025-01',
        maxVideos: 10,
        watchHistoryFile: 'empty-history.json'
      };

      jest.doMock('../services/watch-history.service', () => ({
        WatchHistoryService: {
          loadWatchHistory: jest.fn().mockReturnValue([]),
          extractVideoIds: jest.fn().mockReturnValue([])
        }
      }));

      // Should complete without throwing
      await expect(youtubeAnalyzer.analyzeWatchHistory(config)).resolves.toBeUndefined();
    });

    it('should process watch history successfully', async () => {
      const config = {
        targetMonth: '2025-01',
        maxVideos: 10,
        watchHistoryFile: 'test-history.json'
      };

      const mockHistory = [{
        header: 'YouTube',
        title: 'Test Video',
        titleUrl: 'https://www.youtube.com/watch?v=test123',
        time: '2025-01-15T10:00:00Z',
        products: ['YouTube'],
        activityControls: ['YouTube-Wiedergabeverlauf']
      }];

      jest.doMock('../services/watch-history.service', () => ({
        WatchHistoryService: {
          loadWatchHistory: jest.fn().mockReturnValue(mockHistory),
          extractVideoIds: jest.fn().mockReturnValue(['test123'])
        }
      }));

      jest.spyOn(youtubeAnalyzer as any, 'getVideoDetails').mockResolvedValue({
        id: 'test123',
        title: 'Test Video',
        description: 'Test description',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Test Channel',
        channelId: 'testchannel',
        duration: '10:00'
      });

      mockDispatcher.dispatchBatch.mockResolvedValue([{
        isTechnical: true,
        method: 'keyword-confirmed' as const,
        confidence: 0.95,
        videoId: 'test123',
        title: 'Test Video',
        publishedAt: '2025-01-01T00:00:00Z',
        analysisTimestamp: '2025-01-01T00:00:00Z'
      }]);

      // Should complete without throwing
      await expect(youtubeAnalyzer.analyzeWatchHistory(config)).resolves.toBeUndefined();
    });
  });
});






