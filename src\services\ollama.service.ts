// Ollama service for LLM-based content analysis and summarization [SF][REH]

import { OllamaConfig, TECHNICAL_CATEGORIES } from '../config/ollama';
import {
  VideoMetadata,
  FilterResult,
  EnhancedAnalysisResult,
  OllamaSummaryResponse,
  OllamaAnalysisResponse
} from '../types/analysis';

export class OllamaService {

  // Fixed prompt for summarization (technical content confirmed by keywords) [CMV]
  private readonly SUMMARY_PROMPT = `
Extract 1-3 key technical terms that best summarize this content. Focus on:
- AI/ML technologies (GPT, Claude, LLaMA, transformers, etc.)
- Programming languages/frameworks (Python, React, TypeScript, etc.)
- Development methodologies (Agile, TDD, DevOps, CI/CD, etc.)
- Technical concepts (algorithms, APIs, databases, microservices, etc.)

Respond with JSON only:
{
  "keywords": ["term1", "term2", "term3"],
  "category": "AI|Development|Agile|Testing|Other"
}

Content to analyze:
`;

  // Fixed prompt for analysis + summarization (borderline cases) [CMV]
  private readonly ANALYSIS_PROMPT = `
Analyze if this content is related to AI/ML, Software Development, Agile/Lean methodologies, or Testing.
If technical, extract 1-3 key terms.

Respond with JSON only:
{
  "isTechnical": boolean,
  "confidence": number (0-1),
  "keywords": ["term1", "term2", "term3"],
  "category": "AI|Development|Agile|Testing|Other",
  "reasoning": string (max 50 chars)
}

Content to analyze:
`;

  constructor(private config: OllamaConfig) {}

  /**
   * Summarize already-confirmed technical content [SF]
   */
  async summarize(
    content: string,
    metadata: VideoMetadata,
    keywordResult: FilterResult
  ): Promise<EnhancedAnalysisResult> {
    const context = this.buildContext(content, metadata);
    const fullPrompt = this.SUMMARY_PROMPT + context;

    try {
      const response = await this.callOllama(fullPrompt);
      const parsed = this.parseSummaryResponse(response);

      return {
        isTechnical: true,
        method: 'keyword-confirmed',
        confidence: 0.95,
        matchedKeywords: keywordResult.matchedKeywords,
        aiSummary: parsed.keywords,
        category: parsed.category,
        enhancedBy: 'ollama',
        videoId: metadata.id,
        title: metadata.title,
        publishedAt: metadata.publishedAt,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn(`Ollama summarization failed for ${metadata.id}:`, error);
      // Fallback without Ollama enhancement [REH]
      return {
        isTechnical: true,
        method: 'keyword-confirmed',
        confidence: 0.9,
        matchedKeywords: keywordResult.matchedKeywords,
        videoId: metadata.id,
        title: metadata.title,
        publishedAt: metadata.publishedAt,
        analysisTimestamp: new Date().toISOString(),
        error: 'ollama-unavailable'
      };
    }
  }

  /**
   * Analyze and summarize borderline cases [CA]
   */
  async analyzeAndSummarize(
    content: string,
    metadata: VideoMetadata
  ): Promise<EnhancedAnalysisResult> {
    const context = this.buildContext(content, metadata);
    const fullPrompt = this.ANALYSIS_PROMPT + context;

    try {
      const response = await this.callOllama(fullPrompt);
      const parsed = this.parseAnalysisResponse(response);

      return {
        isTechnical: parsed.isTechnical,
        method: 'ollama-analysis',
        confidence: parsed.confidence,
        aiSummary: parsed.isTechnical ? parsed.keywords : [],
        category: parsed.category,
        reasoning: parsed.reasoning,
        enhancedBy: 'ollama',
        videoId: metadata.id,
        title: metadata.title,
        publishedAt: metadata.publishedAt,
        analysisTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn(`Ollama analysis failed for ${metadata.id}:`, error);
      return this.fallbackResult(content, metadata);
    }
  }

  /**
   * Build context from content and metadata [RP]
   */
  private buildContext(content: string, metadata: VideoMetadata): string {
    return `
Title: ${metadata.title}
Description: ${content.substring(0, 500)}...
Channel: ${metadata.channelTitle}
Duration: ${metadata.duration}
Published: ${metadata.publishedAt}
`;
  }

  /**
   * Call Ollama API with retry logic [REH]
   */
  private async callOllama(prompt: string): Promise<any> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      let timeoutId: NodeJS.Timeout | undefined;
      try {
        const controller = new AbortController();
        timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(`${this.config.baseUrl}/api/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model: this.config.model,
            prompt: prompt,
            stream: false,
            options: {
              temperature: this.config.temperature,
              top_p: this.config.topP
            }
          }),
          signal: controller.signal
        });

        if (!response.ok) {
          throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        lastError = error as Error;
        console.warn(`Ollama attempt ${attempt}/${this.config.maxRetries} failed:`, error);

        if (attempt < this.config.maxRetries) {
          // Exponential backoff [PA] - reduced delays for tests
          const baseDelay = process.env.NODE_ENV === 'test' ? 100 : 1000;
          await this.sleep(Math.pow(2, attempt) * baseDelay);
        }
      } finally {
        // Always clear the timeout to prevent handle leaks [REH]
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      }
    }

    throw lastError!;
  }

  /**
   * Parse summary response with validation [IV]
   */
  private parseSummaryResponse(response: any): OllamaSummaryResponse {
    try {
      const content = response.response || response.content || '';
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        keywords: Array.isArray(parsed.keywords) ? parsed.keywords.slice(0, 3) : [],
        category: this.validateCategory(parsed.category)
      };
    } catch (error) {
      console.warn('Failed to parse summary response:', error);
      return {
        keywords: [],
        category: TECHNICAL_CATEGORIES.OTHER
      };
    }
  }

  /**
   * Parse analysis response with validation [IV]
   */
  private parseAnalysisResponse(response: any): OllamaAnalysisResponse {
    try {
      const content = response.response || response.content || '';
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        isTechnical: Boolean(parsed.isTechnical),
        confidence: Math.min(Math.max(Number(parsed.confidence) || 0, 0), 1),
        keywords: Array.isArray(parsed.keywords) ? parsed.keywords.slice(0, 3) : [],
        category: this.validateCategory(parsed.category),
        reasoning: String(parsed.reasoning || '').substring(0, 50)
      };
    } catch (error) {
      console.warn('Failed to parse analysis response:', error);
      return {
        isTechnical: false,
        confidence: 0.5,
        keywords: [],
        category: TECHNICAL_CATEGORIES.OTHER,
        reasoning: 'Parse error'
      };
    }
  }

  /**
   * Validate and normalize category [IV]
   */
  private validateCategory(category: string): typeof TECHNICAL_CATEGORIES[keyof typeof TECHNICAL_CATEGORIES] {
    const upperCategory = String(category || '').toUpperCase();

    if (Object.values(TECHNICAL_CATEGORIES).includes(upperCategory as any)) {
      return upperCategory as any;
    }

    // Map common variations
    if (upperCategory.includes('AI') || upperCategory.includes('ML')) {
      return TECHNICAL_CATEGORIES.AI;
    }
    if (upperCategory.includes('DEV') || upperCategory.includes('CODE')) {
      return TECHNICAL_CATEGORIES.DEVELOPMENT;
    }
    if (upperCategory.includes('AGILE') || upperCategory.includes('SCRUM')) {
      return TECHNICAL_CATEGORIES.AGILE;
    }
    if (upperCategory.includes('TEST')) {
      return TECHNICAL_CATEGORIES.TESTING;
    }

    return TECHNICAL_CATEGORIES.OTHER;
  }

  /**
   * Fallback result when Ollama fails [REH]
   */
  private fallbackResult(content: string, metadata: VideoMetadata): EnhancedAnalysisResult {
    return {
      isTechnical: false,
      method: 'keyword-exclusion',
      confidence: 0.7,
      videoId: metadata.id,
      title: metadata.title,
      publishedAt: metadata.publishedAt,
      analysisTimestamp: new Date().toISOString(),
      error: 'ollama-failed'
    };
  }

  /**
   * Sleep utility for retry delays [PA]
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
