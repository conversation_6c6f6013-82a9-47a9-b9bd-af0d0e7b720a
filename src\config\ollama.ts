// Ollama configuration for external LLM provider [DM][ISA]

export interface OllamaConfig {
  baseUrl: string;
  model: string;
  timeout: number;
  maxRetries: number;
  temperature: number;
  topP: number;
}

export const defaultOllamaConfig: OllamaConfig = {
  baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
  model: process.env.OLLAMA_MODEL || 'llama3.1:8b',
  timeout: parseInt(process.env.OLLAMA_TIMEOUT || '30000'),
  maxRetries: parseInt(process.env.OLLAMA_MAX_RETRIES || '3'),
  temperature: parseFloat(process.env.OLLAMA_TEMPERATURE || '0.1'), // Consistent results
  topP: parseFloat(process.env.OLLAMA_TOP_P || '0.9')
};

// Technical categories for classification [CMV]
export const TECHNICAL_CATEGORIES = {
  AI: 'AI',
  DEVELOPMENT: 'Development', 
  AGILE: 'Agile',
  TESTING: 'Testing',
  OTHER: 'Other'
} as const;

export type TechnicalCategory = typeof TECHNICAL_CATEGORIES[keyof typeof TECHNICAL_CATEGORIES];
