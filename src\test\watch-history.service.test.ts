// Unit tests for WatchHistoryService [TDT][REH]

import { WatchHistoryService, WatchHistoryEntry } from '../services/watch-history.service';
import * as fs from 'fs';

// Mock fs module
jest.mock('fs');
const mockedFs = fs as jest.Mocked<typeof fs>;

// Suppress console outputs during testing [TDT]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
});

describe('WatchHistoryService', () => {
  const mockWatchHistory: WatchHistoryEntry[] = [
    {
      header: 'YouTube',
      title: 'AI Tutorial - Machine Learning Basics',
      titleUrl: 'https://www.youtube.com/watch?v=abc123',
      time: '2025-01-15T10:30:00Z',
      products: ['YouTube'],
      activityControls: ['YouTube-Wiedergabeverlauf']
    },
    {
      header: 'YouTube',
      title: 'Funny Cat Compilation',
      titleUrl: 'https://www.youtube.com/watch?v=def456',
      time: '2025-01-20T14:15:00Z',
      products: ['YouTube'],
      activityControls: ['YouTube-Wiedergabeverlauf']
    },
    {
      header: 'YouTube',
      title: 'Python Programming Tutorial',
      titleUrl: 'https://www.youtube.com/watch?v=ghi789',
      time: '2024-12-25T09:00:00Z',
      products: ['YouTube'],
      activityControls: ['YouTube-Wiedergabeverlauf']
    },
    {
      header: 'YouTube',
      title: 'Invalid URL Video',
      titleUrl: 'https://www.youtube.com/invalid-url',
      time: '2025-01-10T16:45:00Z',
      products: ['YouTube'],
      activityControls: ['YouTube-Wiedergabeverlauf']
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loadWatchHistory', () => {
    it('should load and parse valid JSON file', () => {
      const mockContent = JSON.stringify(mockWatchHistory);
      mockedFs.readFileSync.mockReturnValue(mockContent);

      const result = WatchHistoryService.loadWatchHistory('test-history.json');

      expect(mockedFs.readFileSync).toHaveBeenCalledWith('test-history.json', 'utf-8');
      expect(result).toEqual(mockWatchHistory);
      expect(result).toHaveLength(4);
    });

    it('should throw error for invalid JSON file', () => {
      mockedFs.readFileSync.mockReturnValue('invalid json content');

      expect(() => {
        WatchHistoryService.loadWatchHistory('invalid.json');
      }).toThrow('Failed to load watch history:');
    });

    it('should throw error when file cannot be read', () => {
      const error = new Error('File not found');
      mockedFs.readFileSync.mockImplementation(() => {
        throw error;
      });

      expect(() => {
        WatchHistoryService.loadWatchHistory('nonexistent.json');
      }).toThrow('Failed to load watch history: Error: File not found');
    });

    it('should handle empty JSON array', () => {
      mockedFs.readFileSync.mockReturnValue('[]');

      const result = WatchHistoryService.loadWatchHistory('empty.json');

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });
  });

  describe('extractVideoIds', () => {
    it('should extract video IDs for specific month', () => {
      const result = WatchHistoryService.extractVideoIds(mockWatchHistory, '2025-01');

      expect(result).toEqual(['abc123', 'def456']);
      expect(result).toHaveLength(2);
    });

    it('should return empty array for month with no videos', () => {
      const result = WatchHistoryService.extractVideoIds(mockWatchHistory, '2025-02');

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('should handle different year correctly', () => {
      const result = WatchHistoryService.extractVideoIds(mockWatchHistory, '2024-12');

      expect(result).toEqual(['ghi789']);
      expect(result).toHaveLength(1);
    });

    it('should filter out invalid URLs', () => {
      const result = WatchHistoryService.extractVideoIds(mockWatchHistory, '2025-01');

      // Should not include the invalid URL video
      expect(result).not.toContain(null);
      expect(result).not.toContain(undefined);
      expect(result).toEqual(['abc123', 'def456']);
    });

    it('should handle edge cases for month boundaries', () => {
      const edgeCaseHistory: WatchHistoryEntry[] = [
        {
          header: 'YouTube',
          title: 'Last day of December',
          titleUrl: 'https://www.youtube.com/watch?v=dec31',
          time: '2024-12-31T23:59:59.999Z',
          products: ['YouTube'],
          activityControls: ['YouTube-Wiedergabeverlauf']
        },
        {
          header: 'YouTube',
          title: 'First day of January',
          titleUrl: 'https://www.youtube.com/watch?v=jan01',
          time: '2025-01-01T00:00:00.000Z',
          products: ['YouTube'],
          activityControls: ['YouTube-Wiedergabeverlauf']
        },
        {
          header: 'YouTube',
          title: 'Last day of January',
          titleUrl: 'https://www.youtube.com/watch?v=jan31',
          time: '2025-01-31T23:59:59.999Z',
          products: ['YouTube'],
          activityControls: ['YouTube-Wiedergabeverlauf']
        }
      ];

      const decemberResult = WatchHistoryService.extractVideoIds(edgeCaseHistory, '2024-12');
      const januaryResult = WatchHistoryService.extractVideoIds(edgeCaseHistory, '2025-01');

      expect(decemberResult).toEqual(['dec31']);
      expect(januaryResult).toEqual(['jan01', 'jan31']);
    });

    it('should handle empty history array', () => {
      const result = WatchHistoryService.extractVideoIds([], '2025-01');

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });
  });

  describe('convertToVideoData', () => {
    const mockYouTubeService = {
      getVideoDetails: jest.fn()
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should convert video IDs to VideoData successfully', async () => {
      const videoIds = ['abc123', 'def456'];
      const mockVideoData1 = {
        id: 'abc123',
        title: 'AI Tutorial',
        description: 'Learn AI basics',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Tech Channel',
        channelId: 'techchannel',
        duration: '10:30'
      };
      const mockVideoData2 = {
        id: 'def456',
        title: 'Cat Video',
        description: 'Funny cats',
        publishedAt: '2025-01-02T00:00:00Z',
        channelTitle: 'Fun Channel',
        channelId: 'funchannel',
        duration: '5:15'
      };

      mockYouTubeService.getVideoDetails
        .mockResolvedValueOnce(mockVideoData1)
        .mockResolvedValueOnce(mockVideoData2);

      const result = await WatchHistoryService.convertToVideoData(videoIds, mockYouTubeService);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(mockVideoData1);
      expect(result[1]).toEqual(mockVideoData2);
      expect(mockYouTubeService.getVideoDetails).toHaveBeenCalledTimes(2);
      expect(mockYouTubeService.getVideoDetails).toHaveBeenCalledWith('abc123');
      expect(mockYouTubeService.getVideoDetails).toHaveBeenCalledWith('def456');
    });

    it('should handle errors gracefully and continue processing', async () => {
      const videoIds = ['abc123', 'invalid', 'def456'];
      const mockVideoData1 = {
        id: 'abc123',
        title: 'AI Tutorial',
        description: 'Learn AI basics',
        publishedAt: '2025-01-01T00:00:00Z',
        channelTitle: 'Tech Channel',
        channelId: 'techchannel',
        duration: '10:30'
      };
      const mockVideoData3 = {
        id: 'def456',
        title: 'Cat Video',
        description: 'Funny cats',
        publishedAt: '2025-01-02T00:00:00Z',
        channelTitle: 'Fun Channel',
        channelId: 'funchannel',
        duration: '5:15'
      };

      mockYouTubeService.getVideoDetails
        .mockResolvedValueOnce(mockVideoData1)
        .mockRejectedValueOnce(new Error('Video not found'))
        .mockResolvedValueOnce(mockVideoData3);

      const result = await WatchHistoryService.convertToVideoData(videoIds, mockYouTubeService);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(mockVideoData1);
      expect(result[1]).toEqual(mockVideoData3);
      expect(mockYouTubeService.getVideoDetails).toHaveBeenCalledTimes(3);
    });

    it('should handle empty video IDs array', async () => {
      const result = await WatchHistoryService.convertToVideoData([], mockYouTubeService);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
      expect(mockYouTubeService.getVideoDetails).not.toHaveBeenCalled();
    });

    it('should handle all failed requests', async () => {
      const videoIds = ['invalid1', 'invalid2'];

      mockYouTubeService.getVideoDetails
        .mockRejectedValueOnce(new Error('Video not found'))
        .mockRejectedValueOnce(new Error('API error'));

      const result = await WatchHistoryService.convertToVideoData(videoIds, mockYouTubeService);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
      expect(mockYouTubeService.getVideoDetails).toHaveBeenCalledTimes(2);
    });
  });

  describe('extractVideoId (private method testing via extractVideoIds)', () => {
    it('should extract video ID from standard YouTube URL', () => {
      const history: WatchHistoryEntry[] = [{
        header: 'YouTube',
        title: 'Test Video',
        titleUrl: 'https://www.youtube.com/watch?v=abc123def',
        time: '2025-01-15T10:00:00Z',
        products: ['YouTube'],
        activityControls: ['YouTube-Wiedergabeverlauf']
      }];

      const result = WatchHistoryService.extractVideoIds(history, '2025-01');
      expect(result).toEqual(['abc123def']);
    });

    it('should extract video ID from URL with additional parameters', () => {
      const history: WatchHistoryEntry[] = [{
        header: 'YouTube',
        title: 'Test Video',
        titleUrl: 'https://www.youtube.com/watch?v=abc123&t=30s&list=playlist',
        time: '2025-01-15T10:00:00Z',
        products: ['YouTube'],
        activityControls: ['YouTube-Wiedergabeverlauf']
      }];

      const result = WatchHistoryService.extractVideoIds(history, '2025-01');
      expect(result).toEqual(['abc123']);
    });

    it('should handle URL with v parameter not first', () => {
      const history: WatchHistoryEntry[] = [{
        header: 'YouTube',
        title: 'Test Video',
        titleUrl: 'https://www.youtube.com/watch?t=30&v=abc123&list=playlist',
        time: '2025-01-15T10:00:00Z',
        products: ['YouTube'],
        activityControls: ['YouTube-Wiedergabeverlauf']
      }];

      const result = WatchHistoryService.extractVideoIds(history, '2025-01');
      expect(result).toEqual(['abc123']);
    });

    it('should return empty array for invalid URLs', () => {
      const history: WatchHistoryEntry[] = [
        {
          header: 'YouTube',
          title: 'Invalid URL 1',
          titleUrl: 'https://www.youtube.com/invalid-url-no-v-param',
          time: '2025-01-15T10:00:00Z',
          products: ['YouTube'],
          activityControls: ['YouTube-Wiedergabeverlauf']
        },
        {
          header: 'YouTube',
          title: 'Invalid URL 2',
          titleUrl: 'https://www.example.com/not-youtube',
          time: '2025-01-15T10:00:00Z',
          products: ['YouTube'],
          activityControls: ['YouTube-Wiedergabeverlauf']
        }
      ];

      const result = WatchHistoryService.extractVideoIds(history, '2025-01');
      expect(result).toEqual([]);
    });
  });
});
