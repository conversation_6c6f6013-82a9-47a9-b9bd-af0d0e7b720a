// Enhanced analysis result types for Ollama integration [SF][RP]

import { TechnicalCategory } from '../config/ollama';

export interface VideoMetadata {
  id: string;
  title: string;
  description: string;
  channelId: string;
  channelTitle: string;
  publishedAt: string;
  duration: string;
}

export interface FilterResult {
  isTechnical: boolean;
  matchedKeywords: string[];
  excludedBy?: string;
}

export interface EnhancedAnalysisResult {
  // Basic classification
  isTechnical: boolean;
  confidence: number;
  method: 'hard-exclusion' | 'keyword-confirmed' | 'ollama-analysis' | 'keyword-exclusion';
  
  // Keyword matching
  matchedKeywords?: string[];
  excludedBy?: string;
  
  // Ollama enhancement [SF]
  aiSummary?: string[];        // 1-3 keywords
  category?: TechnicalCategory; // AI|Development|Agile|Testing
  reasoning?: string;          // Short explanation
  enhancedBy?: 'ollama';
  
  // Metadata
  videoId: string;
  title: string;
  publishedAt: string;
  analysisTimestamp: string;
  error?: string;
}

export interface OllamaSummaryResponse {
  keywords: string[];
  category: TechnicalCategory;
}

export interface OllamaAnalysisResponse {
  isTechnical: boolean;
  confidence: number;
  keywords: string[];
  category: TechnicalCategory;
  reasoning: string;
}
