import * as dotenv from 'dotenv';

// Load environment variables for tests
dotenv.config();

// Automatically adjust timeouts for tests (70% more time)
if (process.env.OLLAMA_TIMEOUT) {
  const prodTimeout = parseInt(process.env.OLLAMA_TIMEOUT);
  const testTimeout = Math.round(prodTimeout * 1.7);
  process.env.OLLAMA_TIMEOUT = testTimeout.toString();
}

// Set NODE_ENV for test environment
process.env.NODE_ENV = 'test';
