# YouTube Technical Content Analyzer

Ein TypeScript-Tool zur Analyse von YouTube-Videos nach technischen Inhalten mit Keyword-Filterung und Schlüsselbegriff-Extraktion.

## Features

- 🔍 **YouTube API Integration**: Analysiert Videos eines bestimmten Monats
- 🎯 **Keyword-Filterung**: Portierte Logik aus `ExtractTechnicalLines.ps1`
- 📊 **Schlüsselbegriff-Extraktion**: Identifiziert häufige technische Begriffe
- 📝 **Transkript-Analyse**: Nutzt YouTube-Transkripte wenn verfügbar
- 📋 **Detaillierte Berichte**: Markdown-Reports mit Statistiken

## Setup

### 1. Dependencies installieren
```bash
cd youtube-analyzer
npm install
```

### 2. YouTube API Key einrichten
1. Gehe zur [Google Cloud Console](https://console.cloud.google.com/)
2. Erstelle ein neues Projekt oder wähle ein bestehendes
3. Aktiviere die YouTube Data API v3
4. E<PERSON><PERSON> einen API Key
5. Kopiere `.env.example` zu `.env`:
```bash
cp .env.example .env
```
6. Füge deinen API Key in `.env` ein:
```
YOUTUBE_API_KEY=dein_api_key_hier
TARGET_MONTH=2025-04
MAX_VIDEOS_PER_CHANNEL=50
```

### 3. Channel IDs konfigurieren
Bearbeite `src/index.ts` und füge die gewünschten YouTube Channel IDs hinzu:
```typescript
const config: AnalysisConfig = {
  channelIds: [
    'UC_x5XG1OV2P6uZZ5FSM9Ttw', // Google DeepMind
    'UCbfYPyITQ-7l4upoX8nvctg', // Two Minute Papers
    // Füge weitere Channel IDs hinzu
  ],
  // ...
};
```

## Verwendung

### Development Mode
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

## Architektur

```
src/
├── config/
│   └── keywords.ts          # Keyword-Konfiguration (aus PS1 portiert)
├── services/
│   ├── youtube.service.ts   # YouTube API Integration
│   └── filter.service.ts    # Content-Filterung
├── utils/
│   └── extractor.ts         # Schlüsselbegriff-Extraktion
└── index.ts                 # Main Application
```

## Keyword-Konfiguration

Die Filterlogik basiert auf drei Kategorien:

- **Include Keywords**: Technische Begriffe (AI, LLM, coding, etc.)
- **Exclude Keywords**: Nicht-technische Kontexte (Movie, Hollywood, etc.)
- **Exclude Hashtags**: Nicht-technische Hashtags (#funny, #comedy, etc.)

Anpassungen in `src/config/keywords.ts` vornehmen.

## Output

Das Tool generiert einen detaillierten Markdown-Report mit:
- Zusammenfassung der analysierten Videos
- Channel-spezifische Statistiken
- Top-Schlüsselbegriffe mit Häufigkeit
- Detaillierte Liste technischer Videos
- Extraktions-Statistiken

## Beispiel-Output

```
# YouTube Technical Content Analysis Report
Generated: 2025-07-28T08:24:33.000Z
Target Month: 2025-04

## Summary
- Total Videos Analyzed: 127
- Technical Videos Found: 45
- Technical Content Ratio: 35.4%

## Top Key Terms
1. **machine learning** (23x)
2. **artificial intelligence** (18x)
3. **neural network** (15x)
...
```

## Troubleshooting

### API Key Probleme
- Stelle sicher, dass die YouTube Data API v3 aktiviert ist
- Prüfe die API Key Berechtigungen
- Beachte die API Rate Limits

### Transkript-Fehler
- Nicht alle Videos haben Transkripte verfügbar
- Das Tool funktioniert auch ohne Transkripte (nur Titel/Beschreibung)

### Performance
- Das Tool implementiert Rate Limiting für API-Requests
- Bei vielen Channels/Videos kann die Analyse länger dauern

## Erweiterungen

- OAuth2 für erweiterte API-Zugriffe
- Zusätzliche Extraktions-Algorithmen
- Web-Interface für einfachere Bedienung
- Batch-Verarbeitung mehrerer Monate
