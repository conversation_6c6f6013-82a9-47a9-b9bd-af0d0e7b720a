# YouTube Technical Content Analyzer

Ein TypeScript-Tool zur Analyse von YouTube-Watch-History nach technischen Inhalten mit intelligenter Keyword-Filterung, Ollama-Integration und Schlüsselbegriff-Extraktion.

## Features

- 🔍 **Watch History Analyse**: Verarbeitet deine YouTube-Watch-History für einen bestimmten Monat
- 🤖 **Ollama LLM-Integration**: Intelligente Analyse und Zusammenfassung von Inhalten
- 🧠 **Smart Dispatching**: 3-Phasen-Architektur für effiziente Inhaltsklassifikation
- 🎯 **Keyword-Filterung**: Erweiterte Logik aus `ExtractTechnicalLines.ps1`
- 📊 **Schlüsselbegriff-Extraktion**: KI-generierte Zusammenfassungen und Kategorisierung
- 📝 **Transkript-Analyse**: Nutzt YouTube-Transkripte wenn verfügbar
- 📋 **Detaillierte Berichte**: Markdown-Reports mit erweiterten Statistiken und Kategorien

## Setup

### 1. Dependencies installieren
```bash
cd youtube-analyzer
npm install
```

### 2. API Keys einrichten
1. Gehe zur [Google Cloud Console](https://console.cloud.google.com/)
2. Erstelle ein neues Projekt oder wähle ein bestehendes
3. Aktiviere die YouTube Data API v3
4. Erstelle einen API Key
5. Kopiere `.env.example` zu `.env`:
```bash
cp .env.example .env
```
6. Füge deine Konfiguration in `.env` ein:
```
# YouTube API Configuration
YOUTUBE_API_KEY=dein_api_key_hier
TARGET_MONTH=2025-04
MAX_VIDEOS_PER_CHANNEL=50

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b
OLLAMA_TIMEOUT=30000
OLLAMA_MAX_RETRIES=3
OLLAMA_TEMPERATURE=0.1
OLLAMA_TOP_P=0.9
```

### 3. Watch History vorbereiten
Lade deine YouTube-Watch-History herunter und platziere sie im Projekt:

1. Gehe zu [Google Takeout](https://takeout.google.com/)
2. Wähle nur "YouTube und YouTube Music" > "Verlauf" > "Watch-History"
3. Exportiere die Daten im JSON-Format
4. Entpacke die heruntergeladene Datei
5. Kopiere die `watch-history.json` in den `data`-Ordner des Projekts:

```bash
mkdir -p data
cp /pfad/zu/deiner/watch-history.json data/
```

## Verwendung

### Development Mode
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

## Architektur

```
src/
├── config/
│   ├── keywords.ts          # Erweiterte Keyword-Konfiguration
│   └── ollama.ts            # Ollama LLM-Konfiguration
├── services/
│   ├── youtube.service.ts   # YouTube API Integration
│   ├── filter.service.ts    # Basis-Content-Filterung
│   ├── ollama.service.ts    # Ollama LLM-Integration
│   ├── smart-dispatcher.ts  # Intelligentes Dispatching-System
│   └── watch-history.service.ts # Watch-History Verarbeitung
├── types/
│   └── analysis.ts          # Typdefinitionen für Analyseergebnisse
├── utils/
│   └── extractor.ts         # Schlüsselbegriff-Extraktion
└── index.ts                 # Main Application
```

## Smart Dispatching System

Das System verwendet eine 3-Phasen-Architektur für intelligentes Content-Dispatching:

### Phase 1: Harte Ausschlüsse (70% der Inhalte)
- Schnelle Filterung von Entertainment/Ablenkungsinhalten
- Musterbasierte Erkennung von nicht-technischen Kategorien
- Spam- und Low-Quality-Erkennung

### Phase 2: Keyword-basierte technische Inhalte (25% der Inhalte)
- Identifikation eindeutig technischer Inhalte durch Keyword-Matching
- Weiterleitung an Ollama für 1-3 Stichwort-Zusammenfassung
- Kategorisierung in AI, Development, Agile, Testing oder Other

### Phase 3: Grenzfälle (5% der Inhalte)
- Analyse von Inhalten mit gemischten Signalen
- Ollama-basierte Klassifikation und Zusammenfassung
- Confidence-Score und Reasoning für Entscheidungstransparenz

## Keyword-Konfiguration

Die erweiterte Filterlogik basiert auf mehreren Kategorien:

- **Include Keywords**: Technische Begriffe (AI, LLM, coding, etc.)
- **Exclude Keywords**: Nicht-technische Kontexte (Movie, Hollywood, etc.)
- **Exclude Hashtags**: Nicht-technische Hashtags (#funny, #comedy, etc.)
- **Technical Categories**: AI, Development, Agile, Testing, Other

Anpassungen in `src/config/keywords.ts` und `src/config/ollama.ts` vornehmen.

## Output

Das Tool generiert einen detaillierten Markdown-Report mit:
- Zusammenfassung der analysierten Videos
- Channel-spezifische Statistiken
- Top-Schlüsselbegriffe mit Häufigkeit
- Technische Kategorien (AI, Development, Agile, Testing, Other)
- Detaillierte Liste technischer Videos mit KI-Zusammenfassungen
- Extraktions-Statistiken und Confidence-Scores
- Dispatching-Methoden-Verteilung

## Beispiel-Output

```
# YouTube Watch History Analysis Report
Generated: 2025-07-28T08:24:33.000Z
Target Month: 2025-04

## Summary
- Total Videos Analyzed: 127
- Technical Videos Found: 45
- Technical Content Ratio: 35.4%
- Ollama Enhanced: 38 videos

## Technical Categories
- AI: 18 videos (40.0%)
- Development: 15 videos (33.3%)
- Agile: 5 videos (11.1%)
- Testing: 4 videos (8.9%)
- Other: 3 videos (6.7%)

## Top Key Terms
1. **machine learning** (23x)
2. **artificial intelligence** (18x)
3. **neural network** (15x)
...

## Dispatching Methods
- Hard Exclusion: 89 videos (70.1%)
- Keyword Confirmed: 32 videos (25.2%)
- Ollama Analysis: 6 videos (4.7%)
```

## Troubleshooting

### API Key Probleme
- Stelle sicher, dass die YouTube Data API v3 aktiviert ist
- Prüfe die API Key Berechtigungen
- Beachte die API Rate Limits

### Watch History Probleme
- Stelle sicher, dass die Watch History im korrekten JSON-Format vorliegt
- Überprüfe, ob Videos für den gewählten Monat in der History existieren
- Bei Problemen mit dem Format, exportiere die Daten erneut aus Google Takeout

### Transkript-Fehler
- Nicht alle Videos haben Transkripte verfügbar
- Das Tool funktioniert auch ohne Transkripte (nur Titel/Beschreibung)

### Ollama-Konfiguration
- Stelle sicher, dass Ollama lokal oder remote verfügbar ist
- Konfiguriere die Ollama-URL und das Modell in `.env`
- Das System implementiert Fallback-Mechanismen bei Ollama-Ausfällen

### Performance
- Das Tool implementiert Rate Limiting für API-Requests
- Smart Dispatching reduziert Ollama-Aufrufe um ca. 70%
- Batch-Verarbeitung mit Pausen zwischen Batches
- Bei vielen Channels/Videos kann die Analyse länger dauern

## Erweiterungen

- OAuth2 für erweiterte API-Zugriffe
- Alternative LLM-Provider (OpenAI, Anthropic, etc.)
- Feintuning der Dispatching-Parameter
- Web-Interface für einfachere Bedienung
- Batch-Verarbeitung mehrerer Monate
- Erweiterte Visualisierungen der Analyseergebnisse

## Ollama Integration

Das System nutzt Ollama für zwei Hauptfunktionen:

### 1. Zusammenfassung technischer Inhalte
- Extraktion von 1-3 Schlüsselbegriffen aus bestätigten technischen Inhalten
- Kategorisierung in technische Bereiche (AI, Development, Agile, Testing, Other)
- Optimierte Prompts für konsistente Ergebnisse

### 2. Analyse von Grenzfällen
- Bestimmung, ob ein Inhalt technisch ist oder nicht
- Confidence-Score für die Klassifikation
- Kurze Begründung für die Entscheidung
- Extraktion von Schlüsselbegriffen bei technischen Inhalten

Die Integration beinhaltet:
- Konfigurierbare Modellparameter (Temperatur, Top-P)
- Retry-Logik mit exponential backoff
- JSON-Response-Parsing mit Validierung
- Fallback-Mechanismen bei Fehlern
