// Main application entry point with smart dispatching [SF][RP][REH]

import * as dotenv from 'dotenv';
import { YouTubeAnalyzer as YouTubeService, WatchHistoryAnalysis } from './services/youtube.service';
import { FilterService } from './services/filter.service';
import { OllamaService } from './services/ollama.service';
import { SmartDispatcher } from './services/smart-dispatcher';
import { defaultKeywordConfig } from './config/keywords';
import { defaultOllamaConfig } from './config/ollama';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config();

interface AnalysisConfig {
  targetMonth: string;
  maxVideos: number;
  outputFile?: string;
  watchHistoryFile: string;
}

class YouTubeAnalyzer {
  private youtubeService: YouTubeService;
  private dispatcher: SmartDispatcher;

  constructor(apiKey: string) {
    // Initialize services [DM][SF]
    const filterService = new FilterService(defaultKeywordConfig);
    const ollamaService = new OllamaService(defaultOllamaConfig);
    this.dispatcher = new SmartDispatcher(filterService, ollamaService);
    this.youtubeService = new YouTubeService(apiKey, this.dispatcher);
  }

  /**
   * Enhanced analysis workflow with smart dispatching [CA][AC]
   */
  async analyze(config: AnalysisConfig): Promise<void> {
    console.log(`🚀 Starting enhanced YouTube analysis for ${config.targetMonth}`);
    console.log(`🤖 Ollama: ${defaultOllamaConfig.baseUrl} (${defaultOllamaConfig.model})`);

    try {
      // Enhanced analysis with smart dispatching [SF]
      await this.youtubeService.analyzeWatchHistory(config);

      console.log(`\n📊 ANALYSIS COMPLETE`);

    } catch (error) {
      throw new Error(`Analysis failed: ${error}`);
    }
  }
}

/**
 * Main execution function [IV][REH]
 */
async function main() {
  // Validate environment [IV]
  const apiKey = process.env.YOUTUBE_API_KEY;
  if (!apiKey) {
    console.error('❌ YOUTUBE_API_KEY not found in environment variables');
    console.log('Please copy .env.example to .env and add your YouTube API key');
    process.exit(1);
  }

  // Configuration [CMV]
  const config: AnalysisConfig = {
    targetMonth: process.env.TARGET_MONTH || '2025-04',
    maxVideos: parseInt(process.env.MAX_VIDEOS_PER_CHANNEL || '50'),
    outputFile: `watch-history-analysis-${process.env.TARGET_MONTH || '2025-04'}.md`,
    watchHistoryFile: 'data/watch-history.json'
  };

  // Validate configuration [IV]
  if (!fs.existsSync(config.watchHistoryFile)) {
    console.error(`❌ Watch history file not found: ${config.watchHistoryFile}`);
    console.log('Please place your YouTube watch history JSON file at the specified path');
    process.exit(1);
  }

  try {
    const analyzer = new YouTubeAnalyzer(apiKey);
    await analyzer.analyze(config);
  } catch (error) {
    console.error('❌ Application failed:', error);
    process.exit(1);
  }
}

// Run if called directly [SF]
if (require.main === module) {
  main().catch(console.error);
}

export { YouTubeAnalyzer, AnalysisConfig };





