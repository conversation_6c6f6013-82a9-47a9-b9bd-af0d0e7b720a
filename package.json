{"name": "youtube-analyzer", "version": "1.0.0", "description": "YouTube technical content analyzer with keyword filtering", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:coverage": "jest --coverage --coverageReporters=text --coverageReporters=lcov", "test:watch": "jest --watch", "test:ci": "jest --coverage --ci --watchAll=false", "coverage:report": "nyc report --reporter=html --reporter=text-summary", "coverage:annotate": "nyc instrument --compact=false src dist/instrumented && nyc report --reporter=html", "clean": "rimraf dist coverage node_modules", "clean:install": "npm run clean && npm install"}, "keywords": ["youtube", "content-analysis", "keyword-filtering"], "author": "", "license": "MIT", "dependencies": {"dotenv": "^16.6.1", "googleapis": "^128.0.0", "youtube-transcript": "^1.2.1"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/jest": "^29.5.5", "@types/node": "^20.19.9", "jest": "^29.7.0", "nyc": "^15.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}