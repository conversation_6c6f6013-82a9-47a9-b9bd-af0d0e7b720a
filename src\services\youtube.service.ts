// YouTube API integration service with smart dispatching [SF][RP][REH]

import { google, youtube_v3 } from 'googleapis';
import { YoutubeTranscript } from 'youtube-transcript';
import { SmartDispatcher } from './smart-dispatcher';
import { VideoMetadata, EnhancedAnalysisResult } from '../types/analysis';

export interface VideoData {
  id: string;
  title: string;
  description: string;
  publishedAt: string;
  channelTitle: string;
  channelId: string;
  duration: string;
  transcript?: string;
}

export interface WatchHistoryAnalysis {
  videos: VideoData[];
  analysisResults: EnhancedAnalysisResult[];
  totalVideos: number;
  technicalVideos: number;
  summary: {
    aiKeywords: string[];
    categories: { [key: string]: number };
    methods: { [key: string]: number };
  };
}

export interface AnalysisConfig {
  watchHistoryFile: string;
  targetMonth: string;
  maxVideos: number;
  outputFile?: string;
}

export class YouTubeAnalyzer {
  private youtube: youtube_v3.Youtube;

  constructor(
    private apiKey: string,
    private dispatcher?: SmartDispatcher
  ) {
    this.youtube = google.youtube({
      version: 'v3',
      auth: apiKey
    });
  }

  /**
   * Analyze watch history videos
   */
  async analyzeWatchHistory(config: AnalysisConfig): Promise<void> {
    console.log(`🚀 Starting watch history analysis for ${config.targetMonth}`);
    
    try {
      // Load watch history
      const { WatchHistoryService } = await import('./watch-history.service');
      const history = WatchHistoryService.loadWatchHistory(config.watchHistoryFile);
      const videoIds = WatchHistoryService.extractVideoIds(history, config.targetMonth);
      
      console.log(`📹 Found ${videoIds.length} videos in watch history for ${config.targetMonth}`);
      
      // Limit videos if needed
      const limitedVideoIds = videoIds.slice(0, config.maxVideos);
      
      // Get video details
      const videos: VideoData[] = [];
      for (const videoId of limitedVideoIds) {
        try {
          const videoData = await this.getVideoDetails(videoId);
          videos.push(videoData);
          await this.delay(100); // Rate limiting
        } catch (error) {
          console.warn(`Could not fetch details for video ${videoId}:`, error);
        }
      }
      
      console.log(`✅ Retrieved details for ${videos.length} videos`);
      
      // Enhanced analysis with smart dispatching
      let analysisResults: EnhancedAnalysisResult[] = [];
      
      if (this.dispatcher && videos.length > 0) {
        console.log(`🤖 Starting smart analysis...`);
        
        const analysisItems = videos.map(video => ({
          content: this.buildVideoContent(video),
          metadata: this.videoToMetadata(video, video.channelId)
        }));
        
        analysisResults = await this.dispatcher.dispatchBatch(analysisItems);
        console.log(`✅ Analysis complete: ${analysisResults.filter(r => r.isTechnical).length}/${analysisResults.length} technical`);
      }
      
      // Generate summary
      const summary = this.generateSummary(analysisResults);
      
      const analysis: WatchHistoryAnalysis = {
        videos,
        analysisResults,
        totalVideos: videos.length,
        technicalVideos: analysisResults.filter(r => r.isTechnical).length,
        summary
      };
      
      // Generate report
      const report = this.generateWatchHistoryReport(analysis, config);
      
      if (config.outputFile) {
        const fs = await import('fs');
        fs.writeFileSync(config.outputFile, report);
        console.log(`📄 Report saved to ${config.outputFile}`);
      }
      
    } catch (error) {
      throw new Error(`Watch history analysis failed: ${error}`);
    }
  }

  private generateWatchHistoryReport(analysis: WatchHistoryAnalysis, config: AnalysisConfig): string {
    return `# YouTube Watch History Analysis Report
Generated: ${new Date().toISOString()}
Target Month: ${config.targetMonth}

## Summary
- **Total Videos Analyzed**: ${analysis.totalVideos}
- **Technical Videos Found**: ${analysis.technicalVideos}
- **Technical Content Ratio**: ${((analysis.technicalVideos / analysis.totalVideos) * 100).toFixed(1)}%

## Analysis Results
${analysis.analysisResults.filter(r => r.isTechnical).map(result => 
  `- **${result.title}** (${result.method})`
).join('\n')}
`;
  }

  /**
   * Get video transcript if available
   */
  private async getVideoTranscript(videoId: string): Promise<string> {
    try {
      const transcript = await YoutubeTranscript.fetchTranscript(videoId);
      return transcript.map(item => item.text).join(' ');
    } catch (error) {
      throw new Error(`Transcript not available for video ${videoId}`);
    }
  }

  /**
   * Get video details
   */
  private async getVideoDetails(videoId: string): Promise<VideoData> {
    try {
      const response = await this.youtube.videos.list({
        part: ['snippet', 'contentDetails'],
        id: [videoId]
      });

      const video = response.data.items?.[0];
      if (!video) {
        throw new Error(`Video ${videoId} not found`);
      }

      const videoData: VideoData = {
        id: video.id! as string,
        title: video.snippet!.title! || '',
        description: video.snippet!.description! || '',
        publishedAt: video.snippet!.publishedAt! || '',
        channelTitle: video.snippet!.channelTitle! || '',
        channelId: video.snippet!.channelId! || '',
        duration: video.contentDetails!.duration! || 'unknown'
      };

      // Attempt to get transcript [REH]
      try {
        const transcript = await this.getVideoTranscript(videoId);
        videoData.transcript = transcript;
      } catch (error) {
        console.warn(`Could not get transcript for video ${videoId}:`, error);
        // Continue without transcript
      }

      return videoData;
    } catch (error) {
      throw new Error(`Failed to get video details: ${error}`);
    }
  }

  /**
   * Build content string from video data
   */
  private buildVideoContent(video: VideoData): string {
    const maxDescriptionLength = 500;
    const maxTranscriptLength = 1000;
    
    let content = `Title: ${video.title}\n`;
    content += `Description: ${video.description.substring(0, maxDescriptionLength)}\n`;
    if (video.transcript) {
      content += `Transcript: ${video.transcript.substring(0, maxTranscriptLength)}`;
    }
    return content;
  }

  /**
   * Convert VideoData to VideoMetadata
   */
  private videoToMetadata(video: VideoData, channelId: string): VideoMetadata {
    return {
      id: video.id,
      title: video.title,
      description: video.description,
      channelId: channelId,
      channelTitle: video.channelTitle,
      publishedAt: video.publishedAt,
      duration: video.duration
    };
  }

  /**
   * Generate summary from analysis results
   */
  private generateSummary(results: EnhancedAnalysisResult[]): {
    aiKeywords: string[];
    categories: { [key: string]: number };
    methods: { [key: string]: number };
  } {
    const allKeywords: string[] = [];
    const categories: { [key: string]: number } = {};
    const methods: { [key: string]: number } = {};

    results.forEach(result => {
      if (result.aiSummary) {
        allKeywords.push(...result.aiSummary);
      }
      if (result.category) {
        categories[result.category] = (categories[result.category] || 0) + 1;
      }
      methods[result.method] = (methods[result.method] || 0) + 1;
    });

    // Remove duplicates
    const uniqueKeywords = [...new Set(allKeywords)];

    return {
      aiKeywords: uniqueKeywords,
      categories,
      methods
    };
  }

  /**
   * Rate limiting delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}







