// Smart dispatcher for intelligent routing between keyword filter and Ollama [SF][CA]

import { FilterService } from './filter.service';
import { OllamaService } from './ollama.service';
import { VideoMetadata, EnhancedAnalysisResult, FilterResult } from '../types/analysis';

export class SmartDispatcher {
  constructor(
    private keywordFilter: FilterService,
    private ollamaService: OllamaService
  ) {}

  /**
   * Main dispatch logic with 3-phase approach [SF][PA]
   */
  async dispatch(content: string, metadata: VideoMetadata): Promise<EnhancedAnalysisResult> {
    // Phase 1: Hard exclusions (entertainment/distraction) [REH]
    const hardExclusion = this.checkHardExclusion(content);
    if (hardExclusion.isExcluded) {
      return {
        isTechnical: false,
        method: 'hard-exclusion',
        confidence: 0.99,
        videoId: metadata.id,
        title: metadata.title,
        publishedAt: metadata.publishedAt,
        analysisTimestamp: new Date().toISOString(),
        excludedBy: hardExclusion.reason
      };
    }

    // Phase 2: Keyword pre-filtering
    const keywordResult = this.keywordFilter.filterContent(content);
    
    if (keywordResult.isTechnical) {
      // Clearly technical -> Ollama for summarization [SF]
      return await this.ollamaService.summarize(content, metadata, keywordResult);
    } else if (this.requiresLLMAnalysis(content, keywordResult)) {
      // Borderline case -> Ollama for classification + summarization
      return await this.ollamaService.analyzeAndSummarize(content, metadata);
    }

    // Phase 3: Definitely non-technical
    return {
      isTechnical: false,
      method: 'keyword-exclusion',
      confidence: 0.9,
      videoId: metadata.id,
      title: metadata.title,
      publishedAt: metadata.publishedAt,
      analysisTimestamp: new Date().toISOString(),
      excludedBy: keywordResult.excludedBy
    };
  }

  /**
   * Check for hard exclusions (entertainment patterns) [CMV]
   */
  private checkHardExclusion(content: string): { isExcluded: boolean; reason?: string } {
    const contentLower = content.toLowerCase();
    
    // Entertainment patterns that should never go to Ollama [PA]
    const entertainmentPatterns = [
      { pattern: /\b(movie|film|series|netflix|disney)\b.*\b(review|trailer|scene)\b/, name: 'Movie/Film content' },
      { pattern: /\b(funny|comedy|meme|viral)\b.*\b(compilation|reaction|challenge)\b/, name: 'Comedy/Entertainment' },
      { pattern: /\b(music|song|dance|concert)\b.*\b(performance|cover|remix)\b/, name: 'Music/Performance' },
      { pattern: /#(funny|comedy|viral|entertainment|shorts|meme|gaming)/, name: 'Entertainment hashtags' },
      { pattern: /\b(cat|dog|pet|animal)\b.*\b(cute|funny|compilation)\b/, name: 'Pet/Animal content' },
      { pattern: /\b(food|cooking|recipe|kitchen)\b.*\b(tutorial|review|taste)\b/, name: 'Food/Cooking content' },
      { pattern: /\b(travel|vacation|vlog|lifestyle)\b/, name: 'Lifestyle content' },
      { pattern: /\b(sports|football|basketball|soccer|tennis|golf)\b/, name: 'Sports content' }
    ];

    for (const { pattern, name } of entertainmentPatterns) {
      if (pattern.test(contentLower)) {
        return { isExcluded: true, reason: name };
      }
    }

    // Check for obvious spam/low-quality content
    if (this.isSpamContent(content)) {
      return { isExcluded: true, reason: 'Spam/Low-quality content' };
    }

    return { isExcluded: false };
  }

  /**
   * Determine if content requires LLM analysis [CA]
   */
  private requiresLLMAnalysis(content: string, keywordResult: FilterResult): boolean {
    // Don't use LLM if already excluded by keywords
    if (keywordResult.excludedBy) {
      return false;
    }

    // Use LLM for ambiguous cases
    const ambiguityIndicators = {
      // Short content is often ambiguous
      tooShort: content.length < 100,
      
      // Mixed signals (some tech terms but not clearly technical)
      mixedSignals: this.hasMixedSignals(content),
      
      // Contains ambiguous tech terms that could be non-technical
      ambiguousTechTerms: this.hasAmbiguousTechTerms(content),
      
      // Context suggests it might be technical but keywords didn't catch it
      potentiallyTechnical: this.mightBeTechnical(content)
    };

    // Use LLM if at least 2 ambiguity indicators are present
    const ambiguityCount = Object.values(ambiguityIndicators).filter(Boolean).length;
    return ambiguityCount >= 2;
  }

  /**
   * Check for mixed signals in content [RP]
   */
  private hasMixedSignals(content: string): boolean {
    const contentLower = content.toLowerCase();
    
    // Has some technical terms but also entertainment terms
    const hasTechTerms = /\b(ai|code|programming|software|algorithm)\b/.test(contentLower);
    const hasEntertainmentTerms = /\b(funny|comedy|movie|game|music|dance)\b/.test(contentLower);
    
    return hasTechTerms && hasEntertainmentTerms;
  }

  /**
   * Check for ambiguous technical terms [RP]
   */
  private hasAmbiguousTechTerms(content: string): boolean {
    const contentLower = content.toLowerCase();
    
    // Terms that could be technical or non-technical depending on context
    const ambiguousPatterns = [
      /\bai\b.*\b(movie|film|robot|character)\b/,  // AI in movies
      /\bcode\b.*\b(secret|hidden|mystery)\b/,     // Secret codes
      /\bmodel\b.*\b(fashion|photo|car)\b/,        // Fashion/car models
      /\balgorithm\b.*\b(youtube|recommendation)\b/ // YouTube algorithm (could be tech or not)
    ];

    return ambiguousPatterns.some(pattern => pattern.test(contentLower));
  }

  /**
   * Check if content might be technical but wasn't caught by keywords [RP]
   */
  private mightBeTechnical(content: string): boolean {
    const contentLower = content.toLowerCase();
    
    // Patterns that suggest technical content
    const technicalIndicators = [
      /\b(tutorial|guide|how to|learn|course)\b.*\b(programming|coding|development)\b/,
      /\b(explain|understand|introduction)\b.*\b(ai|ml|algorithm|software)\b/,
      /\b(build|create|develop)\b.*\b(app|website|software|system)\b/,
      /\b(review|comparison)\b.*\b(framework|library|tool|ide)\b/
    ];

    return technicalIndicators.some(pattern => pattern.test(contentLower));
  }

  /**
   * Detect spam or low-quality content [IV]
   */
  private isSpamContent(content: string): boolean {
    // Repetitive characters
    if (/(.)\1{10,}/.test(content)) {
      return true;
    }

    // Too many emojis
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu) || []).length;
    if (emojiCount > content.length * 0.3) {
      return true;
    }

    // Too many hashtags
    const hashtagCount = (content.match(/#\w+/g) || []).length;
    if (hashtagCount > 10) {
      return true;
    }

    return false;
  }

  /**
   * Batch processing for multiple videos [PA]
   */
  async dispatchBatch(
    items: Array<{ content: string; metadata: VideoMetadata }>
  ): Promise<EnhancedAnalysisResult[]> {
    const results: EnhancedAnalysisResult[] = [];
    const BATCH_SIZE = 5; // Process in small batches to avoid overwhelming Ollama

    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batch = items.slice(i, i + BATCH_SIZE);
      
      console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(items.length / BATCH_SIZE)}`);
      
      const batchPromises = batch.map(item => 
        this.dispatch(item.content, item.metadata)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      // Handle results and errors
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error('Batch processing error:', result.reason);
          // Add error result
          const errorResult: EnhancedAnalysisResult = {
            isTechnical: false,
            method: 'keyword-exclusion',
            confidence: 0.5,
            videoId: 'unknown',
            title: 'Processing failed',
            publishedAt: new Date().toISOString(),
            analysisTimestamp: new Date().toISOString(),
            error: 'batch-processing-failed'
          };
          results.push(errorResult);
        }
      }
      
      // Rate limiting between batches [PA]
      if (i + BATCH_SIZE < items.length) {
        console.log('Waiting 2s before next batch...');
        await this.sleep(2000);
      }
    }

    return results;
  }

  /**
   * Sleep utility [PA]
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
