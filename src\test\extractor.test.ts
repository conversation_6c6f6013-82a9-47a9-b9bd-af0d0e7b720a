// Unit tests for KeyTermExtractor [TDT][REH]

import { KeyTermExtractor, KeyTerm, ExtractionResult } from '../utils/extractor';

// Suppress console outputs during testing [TDT]
const originalLog = console.log;
const originalWarn = console.warn;
const originalError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalLog;
  console.warn = originalWarn;
  console.error = originalError;
});

describe('KeyTermExtractor', () => {
  let extractor: KeyTermExtractor;

  beforeEach(() => {
    extractor = new KeyTermExtractor();
  });

  describe('extractKeyTerms', () => {
    it('should extract key terms from technical content', () => {
      const contents = [
        'Machine learning algorithms are used in artificial intelligence applications',
        'Deep learning neural networks require extensive training data',
        'Python programming language is popular for machine learning projects'
      ];

      const result = extractor.extractKeyTerms(contents, 2, 10);

      expect(result.totalProcessed).toBe(3);
      expect(result.technicalContentCount).toBe(3);
      expect(result.keyTerms.length).toBeGreaterThan(0);

      // Check for expected terms
      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('machine learning');
      expect(termNames).toContain('learning');
    });

    it('should respect minimum frequency threshold', () => {
      const contents = [
        'Machine learning is important',
        'Deep learning is also important',
        'Programming is essential'
      ];

      const result = extractor.extractKeyTerms(contents, 2, 20);

      // Only terms appearing at least 2 times should be included
      const importantTerm = result.keyTerms.find(term => term.term === 'important');
      expect(importantTerm).toBeDefined();
      expect(importantTerm?.frequency).toBe(2);

      // Terms appearing only once should not be included
      const programmingTerm = result.keyTerms.find(term => term.term === 'programming');
      expect(programmingTerm).toBeUndefined();
    });

    it('should limit results to maxTerms', () => {
      const contents = [
        'artificial intelligence machine learning deep learning neural networks',
        'artificial intelligence machine learning deep learning neural networks',
        'artificial intelligence machine learning deep learning neural networks'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 3);

      expect(result.keyTerms.length).toBeLessThanOrEqual(3);
    });

    it('should sort terms by frequency descending', () => {
      const contents = [
        'python python python programming programming data',
        'python python programming data science',
        'python programming data science analysis'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      // Python should appear most frequently (6 times - counting all occurrences)
      const pythonTerm = result.keyTerms.find(term => term.term === 'python');
      expect(pythonTerm).toBeDefined();
      expect(pythonTerm?.frequency).toBe(6);

      // Check that frequencies are in descending order
      for (let i = 1; i < result.keyTerms.length; i++) {
        expect(result.keyTerms[i].frequency).toBeLessThanOrEqual(result.keyTerms[i - 1].frequency);
      }
    });

    it('should handle empty content array', () => {
      const result = extractor.extractKeyTerms([], 1, 10);

      expect(result.totalProcessed).toBe(0);
      expect(result.technicalContentCount).toBe(0);
      expect(result.keyTerms).toHaveLength(0);
    });

    it('should skip empty or whitespace-only content', () => {
      const contents = [
        '',
        '   ',
        'machine learning algorithms',
        '\t\n',
        'deep learning networks'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      expect(result.totalProcessed).toBe(5);
      expect(result.technicalContentCount).toBe(2); // Only non-empty content counted
    });

    it('should extract bigrams correctly', () => {
      const contents = [
        'machine learning and deep learning are important',
        'machine learning algorithms use deep learning techniques'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('machine learning');
      expect(termNames).toContain('deep learning');

      const machineLearningTerm = result.keyTerms.find(term => term.term === 'machine learning');
      expect(machineLearningTerm?.frequency).toBe(2);
    });

    it('should extract trigrams for technical terms', () => {
      const contents = [
        'artificial intelligence systems are complex',
        'artificial intelligence research is advancing',
        'machine learning algorithms work well'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('artificial intelligence');
    });

    it('should provide context for terms', () => {
      const contents = [
        'Machine learning algorithms are used in various applications for data analysis',
        'Deep learning is a subset of machine learning that uses neural networks'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      // Check for either 'machine learning' bigram or individual 'learning' term
      const machineLearningTerm = result.keyTerms.find(term =>
        term.term === 'machine learning' || term.term === 'learning'
      );
      expect(machineLearningTerm).toBeDefined();
      expect(machineLearningTerm?.context).toBeDefined();
      expect(machineLearningTerm?.context.length).toBeGreaterThan(0);
      expect(machineLearningTerm?.context[0]).toContain('learning');
    });

    it('should filter out common words', () => {
      const contents = [
        'the quick brown fox jumps over the lazy dog',
        'the quick brown fox jumps over the lazy dog'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).not.toContain('the');
      expect(termNames).not.toContain('and');
      expect(termNames).not.toContain('is');
    });

    it('should filter out numeric strings', () => {
      const contents = [
        'version 123 of the software includes 456 new features',
        'update 789 fixes 123 bugs and adds 456 improvements'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).not.toContain('123');
      expect(termNames).not.toContain('456');
      expect(termNames).not.toContain('789');
    });

    it('should handle special characters and normalize text', () => {
      const contents = [
        'Machine-learning & AI: the future!',
        'Deep-learning (neural networks) are powerful...',
        'Python programming, JavaScript development.'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('machine-learning');
      expect(termNames).toContain('deep-learning');
    });
  });

  describe('generateSummary', () => {
    it('should generate a meaningful summary', () => {
      const mockResult: ExtractionResult = {
        keyTerms: [
          { term: 'machine learning', frequency: 5, context: ['context1'] },
          { term: 'python', frequency: 4, context: ['context2'] },
          { term: 'data science', frequency: 3, context: ['context3'] },
          { term: 'algorithms', frequency: 2, context: ['context4'] },
          { term: 'neural networks', frequency: 2, context: ['context5'] }
        ],
        totalProcessed: 10,
        technicalContentCount: 8
      };

      const summary = extractor.generateSummary(mockResult);

      expect(summary).toContain('Processed 10 items');
      expect(summary).toContain('8 technical');
      expect(summary).toContain('machine learning (5x)');
      expect(summary).toContain('python (4x)');
      expect(summary).toContain('data science (3x)');
      expect(summary).toContain('algorithms (2x)');
      expect(summary).toContain('neural networks (2x)');
      expect(summary).toContain('Total unique terms: 5');
    });

    it('should handle empty results', () => {
      const mockResult: ExtractionResult = {
        keyTerms: [],
        totalProcessed: 0,
        technicalContentCount: 0
      };

      const summary = extractor.generateSummary(mockResult);

      expect(summary).toContain('Processed 0 items');
      expect(summary).toContain('0 technical');
      expect(summary).toContain('Top terms:');
      expect(summary).toContain('Total unique terms: 0');
    });

    it('should handle results with fewer than 5 terms', () => {
      const mockResult: ExtractionResult = {
        keyTerms: [
          { term: 'python', frequency: 3, context: ['context1'] },
          { term: 'coding', frequency: 2, context: ['context2'] }
        ],
        totalProcessed: 5,
        technicalContentCount: 3
      };

      const summary = extractor.generateSummary(mockResult);

      expect(summary).toContain('python (3x)');
      expect(summary).toContain('coding (2x)');
      expect(summary).toContain('Total unique terms: 2');
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle very long content', () => {
      const longContent = 'machine learning '.repeat(1000) + 'artificial intelligence';
      const contents = [longContent];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      expect(result.technicalContentCount).toBe(1);
      expect(result.keyTerms.length).toBeGreaterThan(0);
    });

    it('should handle content with only common words', () => {
      const contents = [
        'the and or but in on at to for of with by',
        'is are was were be been have has had do does'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      expect(result.technicalContentCount).toBe(2);
      expect(result.keyTerms).toHaveLength(0); // All words filtered out
    });

    it('should handle mixed case content', () => {
      const contents = [
        'MACHINE LEARNING and Deep Learning',
        'Machine Learning AND deep learning'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const machineLearningTerm = result.keyTerms.find(term => term.term === 'machine learning');
      expect(machineLearningTerm?.frequency).toBe(2);
    });

    it('should handle content with only numbers and special characters', () => {
      const contents = [
        '123 456 789 !@# $%^ &*()',
        '111 222 333 []{}|\\:";\'<>?,./'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      expect(result.technicalContentCount).toBe(2);
      expect(result.keyTerms).toHaveLength(0);
    });

    it('should handle very short words', () => {
      const contents = [
        'a b c d e f g h i j k l m n o p q r s t u v w x y z',
        'ai ml dl nn rnn cnn gpt api sql css html js py'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      // Short technical terms should be included
      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('api');
      expect(termNames).toContain('sql');

      // Very short common words should be excluded
      expect(termNames).not.toContain('a');
      expect(termNames).not.toContain('b');
    });
  });

  describe('technical trigram detection', () => {
    it('should detect machine learning trigrams', () => {
      const contents = [
        'machine learning algorithms are powerful',
        'machine learning models need training'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('machine learning algorithms');
    });

    it('should detect artificial intelligence trigrams', () => {
      const contents = [
        'artificial intelligence systems are complex',
        'artificial intelligence research is advancing'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('artificial intelligence systems');
      expect(termNames).toContain('artificial intelligence research');
    });

    it('should detect neural network trigrams', () => {
      const contents = [
        'neural network architecture is important',
        'neural network training requires data'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).toContain('neural network architecture');
      expect(termNames).toContain('neural network training');
    });

    it('should not extract non-technical trigrams', () => {
      const contents = [
        'the quick brown fox jumps over',
        'the lazy dog sleeps all day'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 20);

      const termNames = result.keyTerms.map(term => term.term);
      expect(termNames).not.toContain('the quick brown');
      expect(termNames).not.toContain('the lazy dog');
    });
  });

  describe('context extraction', () => {
    it('should extract context around terms', () => {
      const content = 'This is a long sentence about machine learning algorithms that are used in various applications for data analysis and processing.';
      const contents = [content];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      // Check for either 'machine learning' bigram or individual terms
      const relevantTerm = result.keyTerms.find(term =>
        term.term === 'machine learning' || term.term === 'learning' || term.term === 'algorithms'
      );
      expect(relevantTerm).toBeDefined();
      expect(relevantTerm?.context[0]).toBeDefined();
      expect(relevantTerm?.context[0].length).toBeLessThanOrEqual(content.length);
    });

    it('should handle context at beginning of text', () => {
      const content = 'Machine learning is important for data science applications.';
      const contents = [content];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const relevantTerm = result.keyTerms.find(term =>
        term.term === 'machine learning' || term.term === 'learning'
      );
      expect(relevantTerm).toBeDefined();
      expect(relevantTerm?.context[0]).toContain('learning');
    });

    it('should handle context at end of text', () => {
      const content = 'Data science applications often use machine learning';
      const contents = [content];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const relevantTerm = result.keyTerms.find(term =>
        term.term === 'machine learning' || term.term === 'learning'
      );
      expect(relevantTerm).toBeDefined();
      expect(relevantTerm?.context[0]).toContain('learning');
    });

    it('should limit context examples to 3', () => {
      const contents = [
        'machine learning in context 1',
        'machine learning in context 2',
        'machine learning in context 3',
        'machine learning in context 4',
        'machine learning in context 5'
      ];

      const result = extractor.extractKeyTerms(contents, 1, 10);

      const relevantTerm = result.keyTerms.find(term =>
        term.term === 'machine learning' || term.term === 'learning'
      );
      expect(relevantTerm).toBeDefined();
      expect(relevantTerm?.context.length).toBeLessThanOrEqual(3);
    });
  });
});
